"use client"

import { useState } from "react"
import { StartScreen } from "@/components/start-screen"
import { CaseSelectionScreen } from "@/components/case-selection-screen"
import { NewCaseScreen } from "@/components/new-case-screen"
import { WitnessSetupScreen } from "@/components/witness-setup-screen"
import { RecordingScreen } from "@/components/recording-screen"
import { TranscriptionScreen } from "@/components/transcription-screen"
import { StatementEditScreen } from "@/components/statement-edit-screen"
import { ExportScreen } from "@/components/export-screen"
import { CaseHistoryScreen } from "@/components/case-history-screen"
import { LoadingScreen } from "@/components/loading-screen"

export type Screen =
  | "start-screen"
  | "case-selection"
  | "new-case"
  | "witness-setup"
  | "recording-screen"
  | "transcription-screen"
  | "statement-edit"
  | "export-screen"
  | "case-history"
  | "loading-screen"

export interface Case {
  id: string
  incidentLocation: string
  incidentDate: string
  incidentTime: string
  officer: string
  status: "In Progress" | "Completed"
}

export interface Witness {
  name: string
  type: string
  contact: string
  environment?: string
}

export interface TranscriptSegment {
  speaker: string
  timestamp: string
  text: string
}

export interface TranscriptSpeaker {
  id: string
  name: string
  color: string
}

export interface TranscriptData {
  speakers: TranscriptSpeaker[]
  segments: TranscriptSegment[]
}

export interface AppState {
  currentCase: Case | null
  currentWitness: Witness | null
  isRecording: boolean
  isPaused: boolean
  recordingStartTime: number | null
  transcriptData: TranscriptData | null
  summaryData: string | null
}

export default function Home() {
  const [currentScreen, setCurrentScreen] = useState<Screen>("start-screen")
  const [appState, setAppState] = useState<AppState>({
    currentCase: null,
    currentWitness: null,
    isRecording: false,
    isPaused: false,
    recordingStartTime: null,
    transcriptData: null,
    summaryData: null,
  })

  const navigateToScreen = (screen: Screen) => {
    setCurrentScreen(screen)
  }

  const updateAppState = (updates: Partial<AppState>) => {
    setAppState((prev) => ({ ...prev, ...updates }))
  }

  const resetAppState = () => {
    setAppState({
      currentCase: null,
      currentWitness: null,
      isRecording: false,
      isPaused: false,
      recordingStartTime: null,
      transcriptData: null,
      summaryData: null,
    })
  }

  const renderScreen = () => {
    switch (currentScreen) {
      case "start-screen":
        return <StartScreen onNavigate={navigateToScreen} />
      case "case-selection":
        return <CaseSelectionScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "new-case":
        return <NewCaseScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "witness-setup":
        return <WitnessSetupScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "recording-screen":
        return <RecordingScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "transcription-screen":
        return <TranscriptionScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "statement-edit":
        return <StatementEditScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      case "export-screen":
        return <ExportScreen onNavigate={navigateToScreen} appState={appState} resetAppState={resetAppState} />
      case "case-history":
        return <CaseHistoryScreen onNavigate={navigateToScreen} />
      case "loading-screen":
        return <LoadingScreen onNavigate={navigateToScreen} appState={appState} updateAppState={updateAppState} />
      default:
        return <StartScreen onNavigate={navigateToScreen} />
    }
  }

  return <div className="min-h-screen bg-background">{renderScreen()}</div>
}
