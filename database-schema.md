# FIU Witness Interview Database Schema

## Overview
This database schema is designed for PostgreSQL on Supabase to support the Fire Investigation Unit witness interview management system.

## Tables

### 1. cases
Stores fire investigation case information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | VARCHAR(50) | PRIMARY KEY | Unique case identifier (e.g., FIU-2025-001) |
| incident_location | TEXT | NOT NULL | Location where the incident occurred |
| incident_date | DATE | NOT NULL | Date of the incident |
| incident_time | TIME | NOT NULL | Time of the incident |
| officer | VARCHAR(255) | NOT NULL | Assigned investigating officer |
| status | VARCHAR(20) | NOT NULL, CHECK | Current status (In Progress, Completed) |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Case creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 2. interviews
Stores witness interview information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique interview identifier |
| case_id | VARCHAR(50) | NOT NULL, FOREIGN KEY | Reference to cases.id |
| witness_name | VARCHAR(255) | NOT NULL | Full name of the witness |
| witness_type | VARCHAR(50) | NOT NULL, CHECK | Type of witness (Resident, Neighbor, etc.) |
| witness_contact | VARCHAR(255) | | Contact information |
| interview_environment | VARCHAR(20) | CHECK | Interview environment (controlled, field) |
| status | VARCHAR(20) | NOT NULL, CHECK | Interview status (scheduled, in_progress, completed, cancelled) |
| start_time | TIMESTAMPTZ | | Interview start time |
| end_time | TIMESTAMPTZ | | Interview end time |
| duration_seconds | INTEGER | | Interview duration in seconds |
| recording_path | TEXT | | Path to the audio recording file |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Interview creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 3. transcription_speakers
Stores speaker information for transcriptions.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique speaker identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY | Reference to interviews.id |
| speaker_id | VARCHAR(10) | NOT NULL | Speaker identifier (S1, S2, etc.) |
| name | VARCHAR(255) | NOT NULL | Speaker name |
| color | VARCHAR(7) | NOT NULL | Color code for UI display |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |

### 4. transcription_segments
Stores individual transcription segments.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique segment identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY | Reference to interviews.id |
| speaker_id | VARCHAR(10) | NOT NULL | Speaker identifier |
| timestamp | VARCHAR(10) | NOT NULL | Timestamp in MM:SS format |
| text | TEXT | NOT NULL | Transcribed text |
| confidence | DECIMAL(3,2) | CHECK (confidence >= 0 AND confidence <= 1) | Transcription confidence score |
| sequence_number | INTEGER | NOT NULL | Order of segment in interview |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Creation timestamp |

### 5. statements
Stores formal interview statements.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique statement identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY, UNIQUE | Reference to interviews.id |
| content | TEXT | NOT NULL | Formal statement content |
| officer_notes | TEXT | | Additional officer notes |
| version | INTEGER | NOT NULL, DEFAULT 1 | Statement version number |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Statement creation timestamp |
| updated_at | TIMESTAMPTZ | DEFAULT NOW() | Last update timestamp |

### 6. audio_recordings
Stores audio recording metadata and file information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique recording identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY, UNIQUE | Reference to interviews.id |
| file_path | TEXT | NOT NULL | Path to audio file in storage |
| file_size | BIGINT | | File size in bytes |
| duration_seconds | INTEGER | | Recording duration in seconds |
| format | VARCHAR(10) | | Audio format (mp3, wav, etc.) |
| sample_rate | INTEGER | | Audio sample rate |
| channels | INTEGER | | Number of audio channels |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Recording creation timestamp |

### 7. export_logs
Tracks document exports for auditing purposes.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY, DEFAULT gen_random_uuid() | Unique export identifier |
| interview_id | UUID | NOT NULL, FOREIGN KEY | Reference to interviews.id |
| export_type | VARCHAR(10) | NOT NULL, CHECK | Export format (pdf, docx) |
| file_path | TEXT | | Path to exported file |
| exported_by | VARCHAR(255) | | User who performed the export |
| created_at | TIMESTAMPTZ | DEFAULT NOW() | Export timestamp |

## Indexes

### Performance Indexes
- `idx_cases_status` on cases(status)
- `idx_cases_officer` on cases(officer)
- `idx_cases_incident_date` on cases(incident_date)
- `idx_interviews_case_id` on interviews(case_id)
- `idx_interviews_status` on interviews(status)
- `idx_transcription_segments_interview_id` on transcription_segments(interview_id)
- `idx_transcription_segments_sequence` on transcription_segments(interview_id, sequence_number)

## Row Level Security (RLS)

Supabase RLS policies will be implemented to ensure:
- Officers can only access cases assigned to them
- Interview data is protected based on case access
- Transcription data follows the same access patterns as interviews
- Export logs maintain audit trail integrity

## Storage Buckets

### audio-recordings
- Stores audio recording files
- Access controlled by RLS policies
- Automatic cleanup policies for old recordings

### exported-documents
- Stores generated PDF and Word documents
- Temporary storage with automatic cleanup
- Access controlled by RLS policies

## Triggers

### updated_at Triggers
Automatic timestamp updates for:
- cases.updated_at
- interviews.updated_at
- statements.updated_at

### Audit Triggers
- Log changes to sensitive data
- Track access patterns for compliance

## Relationships

```
cases (1) ──── (many) interviews
interviews (1) ──── (many) transcription_speakers
interviews (1) ──── (many) transcription_segments
interviews (1) ──── (1) statements
interviews (1) ──── (1) audio_recordings
interviews (1) ──── (many) export_logs
```

## Data Validation

### Check Constraints
- case.status IN ('In Progress', 'Completed')
- interview.status IN ('scheduled', 'in_progress', 'completed', 'cancelled')
- witness_type IN ('Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder')
- interview_environment IN ('controlled', 'field')
- export_type IN ('pdf', 'docx')
- confidence BETWEEN 0 AND 1

### Business Rules
- end_time must be after start_time when both are set
- duration_seconds should match calculated time difference
- Only one active interview per case at a time
- Statement version increments on updates
