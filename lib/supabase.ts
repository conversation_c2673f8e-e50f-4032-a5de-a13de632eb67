// Supabase client configuration and database operations
import { createClient } from '@supabase/supabase-js';
import type {
  DatabaseCase,
  DatabaseInterview,
  DatabaseTranscriptionSpeaker,
  DatabaseTranscriptionSegment,
  DatabaseStatement,
  DatabaseAudioRecording,
  DatabaseExportLog,
  Case,
  Interview,
  TranscriptData,
  Statement,
  AudioRecording,
  ExportLog,
  CaseFilters,
  InterviewFilters,
  CreateCaseRequest,
  CreateInterviewRequest,
  UpdateCaseRequest,
  UpdateInterviewRequest,
  UpdateStatementRequest,
  SupabaseResponse,
} from '@/types/database';
import {
  transformDatabaseCase,
  transformDatabaseInterview,
  transformDatabaseTranscriptionData,
  transformDatabaseStatement,
  transformDatabaseAudioRecording,
  transformDatabaseExportLog,
  transformCaseToDatabase,
  transformInterviewToDatabase,
  transformStatementToDatabase,
  transformTranscriptionSpeakerToDatabase,
  transformTranscriptionSegmentToDatabase,
} from './database-transformers';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Case operations
export class CaseService {
  static async getCases(filters: CaseFilters = {}): Promise<Case[]> {
    let query = supabase
      .from('cases')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.officer) {
      query = query.eq('officer', filters.officer);
    }
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    const { data, error } = await query;
    if (error) throw error;
    
    return (data as DatabaseCase[]).map(transformDatabaseCase);
  }

  static async getCaseById(id: string): Promise<Case | null> {
    const { data, error } = await supabase
      .from('cases')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }

    return transformDatabaseCase(data as DatabaseCase);
  }

  static async createCase(caseData: CreateCaseRequest): Promise<Case> {
    const { data, error } = await supabase
      .from('cases')
      .insert([caseData])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseCase(data as DatabaseCase);
  }

  static async updateCase(id: string, updates: UpdateCaseRequest): Promise<Case> {
    const { data, error } = await supabase
      .from('cases')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseCase(data as DatabaseCase);
  }

  static async deleteCase(id: string): Promise<void> {
    const { error } = await supabase
      .from('cases')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }
}

// Interview operations
export class InterviewService {
  static async getInterviews(filters: InterviewFilters = {}): Promise<Interview[]> {
    let query = supabase
      .from('interviews')
      .select('*')
      .order('created_at', { ascending: false });

    if (filters.caseId) {
      query = query.eq('case_id', filters.caseId);
    }
    if (filters.status) {
      query = query.eq('status', filters.status);
    }
    if (filters.limit) {
      query = query.limit(filters.limit);
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);
    }

    const { data, error } = await query;
    if (error) throw error;

    return (data as DatabaseInterview[]).map(transformDatabaseInterview);
  }

  static async getInterviewById(id: string): Promise<Interview | null> {
    const { data, error } = await supabase
      .from('interviews')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async createInterview(caseId: string, interviewData: CreateInterviewRequest): Promise<Interview> {
    const dbData = {
      case_id: caseId,
      witness_name: interviewData.witness.name,
      witness_type: interviewData.witness.type,
      witness_contact: interviewData.witness.contact,
      interview_environment: interviewData.witness.environment,
      scheduled_time: interviewData.scheduled_time,
    };

    const { data, error } = await supabase
      .from('interviews')
      .insert([dbData])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async updateInterview(id: string, updates: UpdateInterviewRequest): Promise<Interview> {
    const dbUpdates: Partial<DatabaseInterview> = {};
    
    if (updates.witness) {
      dbUpdates.witness_name = updates.witness.name;
      dbUpdates.witness_type = updates.witness.type;
      dbUpdates.witness_contact = updates.witness.contact;
      dbUpdates.interview_environment = updates.witness.environment;
    }
    if (updates.status) {
      dbUpdates.status = updates.status;
    }
    if (updates.end_time) {
      dbUpdates.end_time = updates.end_time;
    }

    const { data, error } = await supabase
      .from('interviews')
      .update(dbUpdates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async startInterview(id: string): Promise<Interview> {
    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'in_progress',
        start_time: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }

  static async endInterview(id: string): Promise<Interview> {
    const interview = await this.getInterviewById(id);
    if (!interview || !interview.startTime) {
      throw new Error('Interview not found or not started');
    }

    const endTime = new Date();
    const duration = Math.floor((endTime.getTime() - interview.startTime.getTime()) / 1000);

    const { data, error } = await supabase
      .from('interviews')
      .update({
        status: 'completed',
        end_time: endTime.toISOString(),
        duration_seconds: duration,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseInterview(data as DatabaseInterview);
  }
}

// Transcription operations
export class TranscriptionService {
  static async getTranscriptionData(interviewId: string): Promise<TranscriptData | null> {
    const [speakersResult, segmentsResult] = await Promise.all([
      supabase
        .from('transcription_speakers')
        .select('*')
        .eq('interview_id', interviewId),
      supabase
        .from('transcription_segments')
        .select('*')
        .eq('interview_id', interviewId)
        .order('sequence_number'),
    ]);

    if (speakersResult.error) throw speakersResult.error;
    if (segmentsResult.error) throw segmentsResult.error;

    if (!speakersResult.data?.length && !segmentsResult.data?.length) {
      return null;
    }

    return transformDatabaseTranscriptionData(
      speakersResult.data as DatabaseTranscriptionSpeaker[],
      segmentsResult.data as DatabaseTranscriptionSegment[]
    );
  }

  static async saveTranscriptionData(interviewId: string, transcriptData: TranscriptData): Promise<void> {
    // Save speakers
    const speakersData = transcriptData.speakers.map(speaker =>
      transformTranscriptionSpeakerToDatabase(speaker, interviewId)
    );

    const { error: speakersError } = await supabase
      .from('transcription_speakers')
      .upsert(speakersData, { onConflict: 'interview_id,speaker_id' });

    if (speakersError) throw speakersError;

    // Save segments
    const segmentsData = transcriptData.segments.map((segment, index) =>
      transformTranscriptionSegmentToDatabase(segment, interviewId, index + 1)
    );

    const { error: segmentsError } = await supabase
      .from('transcription_segments')
      .upsert(segmentsData, { onConflict: 'interview_id,sequence_number' });

    if (segmentsError) throw segmentsError;
  }

  static async addTranscriptionSegment(
    interviewId: string,
    segment: { speaker: string; timestamp: string; text: string; confidence?: number }
  ): Promise<void> {
    // Get the next sequence number
    const { data: lastSegment } = await supabase
      .from('transcription_segments')
      .select('sequence_number')
      .eq('interview_id', interviewId)
      .order('sequence_number', { ascending: false })
      .limit(1)
      .single();

    const sequenceNumber = (lastSegment?.sequence_number || 0) + 1;

    const segmentData = transformTranscriptionSegmentToDatabase(segment, interviewId, sequenceNumber);

    const { error } = await supabase
      .from('transcription_segments')
      .insert([segmentData]);

    if (error) throw error;
  }
}

// Statement operations
export class StatementService {
  static async getStatement(interviewId: string): Promise<Statement | null> {
    const { data, error } = await supabase
      .from('statements')
      .select('*')
      .eq('interview_id', interviewId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseStatement(data as DatabaseStatement);
  }

  static async createStatement(interviewId: string, content: string, officerNotes?: string): Promise<Statement> {
    const { data, error } = await supabase
      .from('statements')
      .insert([{
        interview_id: interviewId,
        content,
        officer_notes: officerNotes,
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseStatement(data as DatabaseStatement);
  }

  static async updateStatement(interviewId: string, updates: UpdateStatementRequest): Promise<Statement> {
    const { data, error } = await supabase
      .from('statements')
      .update({
        content: updates.content,
        officer_notes: updates.officer_notes,
      })
      .eq('interview_id', interviewId)
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseStatement(data as DatabaseStatement);
  }
}

// Audio recording operations
export class AudioRecordingService {
  static async getAudioRecording(interviewId: string): Promise<AudioRecording | null> {
    const { data, error } = await supabase
      .from('audio_recordings')
      .select('*')
      .eq('interview_id', interviewId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') return null;
      throw error;
    }

    return transformDatabaseAudioRecording(data as DatabaseAudioRecording);
  }

  static async createAudioRecording(
    interviewId: string,
    filePath: string,
    metadata: {
      fileSize?: number;
      duration?: number;
      format?: string;
      sampleRate?: number;
      channels?: number;
    }
  ): Promise<AudioRecording> {
    const { data, error } = await supabase
      .from('audio_recordings')
      .insert([{
        interview_id: interviewId,
        file_path: filePath,
        file_size: metadata.fileSize,
        duration_seconds: metadata.duration,
        format: metadata.format,
        sample_rate: metadata.sampleRate,
        channels: metadata.channels,
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseAudioRecording(data as DatabaseAudioRecording);
  }
}

// Export log operations
export class ExportLogService {
  static async getExportLogs(interviewId: string): Promise<ExportLog[]> {
    const { data, error } = await supabase
      .from('export_logs')
      .select('*')
      .eq('interview_id', interviewId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return (data as DatabaseExportLog[]).map(transformDatabaseExportLog);
  }

  static async logExport(
    interviewId: string,
    exportType: 'pdf' | 'docx',
    filePath?: string,
    exportedBy?: string
  ): Promise<ExportLog> {
    const { data, error } = await supabase
      .from('export_logs')
      .insert([{
        interview_id: interviewId,
        export_type: exportType,
        file_path: filePath,
        exported_by: exportedBy,
      }])
      .select()
      .single();

    if (error) throw error;
    return transformDatabaseExportLog(data as DatabaseExportLog);
  }
}

// Storage operations
export class StorageService {
  static async uploadAudioFile(interviewId: string, file: File): Promise<string> {
    const fileName = `${interviewId}/${Date.now()}-${file.name}`;
    
    const { data, error } = await supabase.storage
      .from('audio-recordings')
      .upload(fileName, file);

    if (error) throw error;
    return data.path;
  }

  static async getAudioFileUrl(filePath: string): Promise<string> {
    const { data } = supabase.storage
      .from('audio-recordings')
      .getPublicUrl(filePath);

    return data.publicUrl;
  }

  static async uploadExportFile(interviewId: string, file: Blob, fileName: string): Promise<string> {
    const filePath = `${interviewId}/${Date.now()}-${fileName}`;
    
    const { data, error } = await supabase.storage
      .from('exported-documents')
      .upload(filePath, file);

    if (error) throw error;
    return data.path;
  }

  static async getExportFileUrl(filePath: string): Promise<string> {
    const { data } = supabase.storage
      .from('exported-documents')
      .getPublicUrl(filePath);

    return data.publicUrl;
  }
}

// Real-time subscriptions
export class RealtimeService {
  static subscribeToInterview(interviewId: string, callback: (interview: Interview) => void) {
    return supabase
      .channel(`interview-${interviewId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'interviews',
          filter: `id=eq.${interviewId}`,
        },
        (payload) => {
          const interview = transformDatabaseInterview(payload.new as DatabaseInterview);
          callback(interview);
        }
      )
      .subscribe();
  }

  static subscribeToTranscriptionSegments(interviewId: string, callback: (segment: any) => void) {
    return supabase
      .channel(`transcription-${interviewId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'transcription_segments',
          filter: `interview_id=eq.${interviewId}`,
        },
        (payload) => {
          callback(payload.new);
        }
      )
      .subscribe();
  }
}
