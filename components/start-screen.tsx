"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import type { Screen } from "@/app/page"

interface StartScreenProps {
  onNavigate: (screen: Screen) => void
}

export function StartScreen({ onNavigate }: StartScreenProps) {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold text-primary mb-2">Fire Investigation Unit</h1>
        <p className="text-xl text-muted-foreground">Interview System</p>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          <div className="text-center mb-6">
            <h3 className="text-lg font-semibold mb-4">Officer Authentication</h3>
            <div className="bg-secondary p-4 rounded-lg space-y-2">
              <p className="font-medium"><PERSON></p>
              <p className="text-sm text-muted-foreground">Badge: FIU-2847</p>
              <p className="text-sm text-muted-foreground">Fire Investigation Unit</p>
              <p className="text-sm text-muted-foreground">Central Division</p>
            </div>
          </div>

          <div className="space-y-3">
            <Button className="w-full" size="lg" onClick={() => onNavigate("case-selection")}>
              Start New Interview
            </Button>
            <Button variant="secondary" className="w-full" onClick={() => onNavigate("case-history")}>
              View Case History
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
