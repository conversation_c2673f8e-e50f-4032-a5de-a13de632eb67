"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowLeft } from "lucide-react"
import type { Screen, Case, AppState } from "@/app/page"

interface NewCaseScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

export function NewCaseScreen({ onNavigate, appState, updateAppState }: NewCaseScreenProps) {
  const [formData, setFormData] = useState({
    caseId: "FIU-2025-003",
    incidentLocation: "",
    incidentDate: "",
    incidentTime: "",
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    const newCase: Case = {
      id: formData.caseId,
      incidentLocation: formData.incidentLocation,
      incidentDate: formData.incidentDate,
      incidentTime: formData.incidentTime,
      officer: "<PERSON> <PERSON>",
      status: "In Progress",
    }

    updateAppState({ currentCase: newCase })
    onNavigate("witness-setup")
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex items-center justify-between mb-8 pb-4 border-b">
        <Button variant="outline" size="sm" onClick={() => onNavigate("case-selection")}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        <h2 className="text-2xl font-semibold">New Case Setup</h2>
        <div></div>
      </div>

      <Card className="max-w-md mx-auto">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="case-id">Case ID</Label>
              <Input id="case-id" value={formData.caseId} readOnly className="bg-muted" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="incident-location">Incident Location</Label>
              <Input
                id="incident-location"
                value={formData.incidentLocation}
                onChange={(e) => handleInputChange("incidentLocation", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="incident-date">Incident Date</Label>
              <Input
                id="incident-date"
                type="date"
                value={formData.incidentDate}
                onChange={(e) => handleInputChange("incidentDate", e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="incident-time">Incident Time</Label>
              <Input
                id="incident-time"
                type="time"
                value={formData.incidentTime}
                onChange={(e) => handleInputChange("incidentTime", e.target.value)}
                required
              />
            </div>

            <Button type="submit" className="w-full">
              Create Case
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
