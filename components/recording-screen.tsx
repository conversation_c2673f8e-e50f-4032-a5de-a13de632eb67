"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Pause, Play, Square } from "lucide-react"
import type { Screen, AppState, TranscriptData } from "@/app/page"

interface RecordingScreenProps {
  onNavigate: (screen: Screen) => void
  appState: AppState
  updateAppState: (updates: Partial<AppState>) => void
}

const mockTranscriptData: TranscriptData = {
  speakers: [
    { id: "S1", name: "<PERSON>", color: "#2563eb" },
    { id: "S2", name: "<PERSON>", color: "#dc3545" },
  ],
  segments: [
    {
      speaker: "S1",
      timestamp: "00:00",
      text: "This is <PERSON> with the Fire Investigation Unit. Today is June 24th, 2025, and I'm conducting an interview regarding the incident at 123 Main Street. Could you please state your name for the record?",
    },
    {
      speaker: "S2",
      timestamp: "00:15",
      text: "My name is <PERSON>. I live in apartment 3A, right next door to where the fire happened.",
    },
    {
      speaker: "S1",
      timestamp: "00:25",
      text: "Thank you, <PERSON>. Can you tell me what you observed on the day of the incident?",
    },
    {
      speaker: "S2",
      timestamp: "00:32",
      text: "I was home around 2:30 PM when I heard a loud bang from next door. At first I thought maybe someone dropped something heavy, but then I started smelling smoke.",
    },
    { speaker: "S1", timestamp: "00:45", text: "What did you do when you smelled the smoke?" },
    {
      speaker: "S2",
      timestamp: "00:48",
      text: "I immediately went to my window and looked outside. I could see smoke coming from their kitchen window. That's when I called 911 and then knocked on their door to make sure everyone was okay.",
    },
  ],
}

const mockSummary =
  "Witness Sarah Williams, residing in apartment 3A at 125 Main Street, reported hearing a loud bang from the adjacent apartment 4B at approximately 2:30 PM on June 20th, 2025. Upon detecting smoke odor, she observed smoke emanating from the kitchen window of the affected unit. Williams promptly contacted emergency services and attempted to ensure the safety of the occupants. Her account provides valuable timeline information and corroborates the initial fire location assessment."

export function RecordingScreen({ onNavigate, appState, updateAppState }: RecordingScreenProps) {
  const [recordingTime, setRecordingTime] = useState("00:00")
  const [startTime, setStartTime] = useState<number | null>(null)

  useEffect(() => {
    // Auto-start recording when component mounts
    if (!appState.isRecording) {
      handleStartRecording()
    }
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null

    if (appState.isRecording && !appState.isPaused && startTime) {
      interval = setInterval(() => {
        const elapsed = Date.now() - startTime
        const minutes = Math.floor(elapsed / 60000)
        const seconds = Math.floor((elapsed % 60000) / 1000)
        setRecordingTime(`${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [appState.isRecording, appState.isPaused, startTime])

  const handleStartRecording = () => {
    const now = Date.now()
    setStartTime(now)
    updateAppState({
      isRecording: true,
      isPaused: false,
      recordingStartTime: now,
    })
  }

  const handlePauseRecording = () => {
    updateAppState({ isPaused: true })
  }

  const handleResumeRecording = () => {
    updateAppState({ isPaused: false })
  }

  const handleStopRecording = () => {
    updateAppState({
      isRecording: false,
      isPaused: false,
    })

    // Show loading screen
    onNavigate("loading-screen")

    // Simulate processing
    setTimeout(() => {
      updateAppState({
        transcriptData: mockTranscriptData,
        summaryData: mockSummary,
      })
      onNavigate("transcription-screen")
    }, 3000)
  }

  return (
    <div className="container mx-auto px-4 py-8 flex flex-col h-screen">
      <div className="flex items-center justify-between mb-8 pb-4 border-b flex-shrink-0">
        <h2 className="text-2xl font-semibold">Recording Interview</h2>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
          <Badge variant="secondary">{recordingTime}</Badge>
        </div>
      </div>

      <Card className="mb-8 flex-shrink-0">
        <CardContent className="p-4">
          <div className="space-y-2">
            <p>
              <strong>Case:</strong> {appState.currentCase?.id}
            </p>
            <p>
              <strong>Witness:</strong> {appState.currentWitness?.name}
            </p>
          </div>
        </CardContent>
      </Card>

      <Card className="mb-8 flex-1">
        <CardContent className="p-8">
          <div className="max-h-96 overflow-y-auto p-4">
            {mockTranscriptData?.segments.map((segment, index) => {
              const speaker = mockTranscriptData?.speakers.find((s) => s.id === segment.speaker)
              return (
                <div key={index} className="flex gap-4 mb-4">
                  <div
                    className="min-w-[120px] text-right pr-3 border-r-2 text-sm font-medium"
                    style={{ borderRightColor: speaker?.color }}
                  >
                    {speaker?.name}
                    <div className="text-xs text-muted-foreground font-normal">{segment.timestamp}</div>
                  </div>
                  <div className="flex-1 text-sm leading-relaxed">{segment.text}</div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-col gap-4 max-w-sm mx-auto flex-shrink-0">
        {!appState.isPaused ? (
          <Button variant="secondary" size="lg" onClick={handlePauseRecording} className="flex items-center gap-2">
            <Pause className="w-5 h-5" />
            Pause
          </Button>
        ) : (
          <Button variant="secondary" size="lg" onClick={handleResumeRecording} className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            Resume
          </Button>
        )}

        <Button size="lg" onClick={handleStopRecording} className="flex items-center gap-2">
          <Square className="w-5 h-5" />
          Stop Interview
        </Button>
      </div>
    </div>
  )
}
