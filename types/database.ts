// Database types for FIU Witness Interview System
// Generated from PostgreSQL schema

export type CaseStatus = 'In Progress' | 'Completed';
export type InterviewStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
export type WitnessType = 'Resident' | 'Neighbor' | 'Passerby' | 'Business Owner' | 'Emergency Responder';
export type InterviewEnvironment = 'controlled' | 'field';
export type ExportType = 'pdf' | 'docx';

// Database table interfaces
export interface DatabaseCase {
  id: string;
  incident_location: string;
  incident_date: string; // ISO date string
  incident_time: string; // HH:MM format
  officer: string;
  status: CaseStatus;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseInterview {
  id: string;
  case_id: string;
  witness_name: string;
  witness_type: WitnessType;
  witness_contact: string | null;
  interview_environment: InterviewEnvironment | null;
  status: InterviewStatus;
  start_time: string | null; // ISO datetime string
  end_time: string | null; // ISO datetime string
  duration_seconds: number | null;
  recording_path: string | null;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseTranscriptionSpeaker {
  id: string;
  interview_id: string;
  speaker_id: string; // S1, S2, etc.
  name: string;
  color: string; // Hex color code
  created_at: string; // ISO datetime string
}

export interface DatabaseTranscriptionSegment {
  id: string;
  interview_id: string;
  speaker_id: string;
  timestamp: string; // MM:SS format
  text: string;
  confidence: number | null; // 0-1 range
  sequence_number: number;
  created_at: string; // ISO datetime string
}

export interface DatabaseStatement {
  id: string;
  interview_id: string;
  content: string;
  officer_notes: string | null;
  version: number;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

export interface DatabaseAudioRecording {
  id: string;
  interview_id: string;
  file_path: string;
  file_size: number | null;
  duration_seconds: number | null;
  format: string | null;
  sample_rate: number | null;
  channels: number | null;
  created_at: string; // ISO datetime string
}

export interface DatabaseExportLog {
  id: string;
  interview_id: string;
  export_type: ExportType;
  file_path: string | null;
  exported_by: string | null;
  created_at: string; // ISO datetime string
}

// API request/response types
export interface CreateCaseRequest {
  incident_location: string;
  incident_date: string;
  incident_time: string;
  officer: string;
}

export interface UpdateCaseRequest {
  incident_location?: string;
  incident_date?: string;
  incident_time?: string;
  officer?: string;
  status?: CaseStatus;
}

export interface CreateInterviewRequest {
  witness: {
    name: string;
    type: WitnessType;
    contact: string;
    environment?: InterviewEnvironment;
  };
  scheduled_time?: string;
}

export interface UpdateInterviewRequest {
  witness?: {
    name: string;
    type: WitnessType;
    contact: string;
    environment?: InterviewEnvironment;
  };
  status?: InterviewStatus;
  end_time?: string;
}

export interface UpdateStatementRequest {
  content: string;
  officer_notes?: string;
}

// Frontend model interfaces (transformed from database types)
export interface Case {
  id: string;
  incidentLocation: string;
  incidentDate: string;
  incidentTime: string;
  officer: string;
  status: CaseStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface Witness {
  name: string;
  type: WitnessType;
  contact: string;
  environment?: InterviewEnvironment;
}

export interface Interview {
  id: string;
  caseId: string;
  witness: Witness;
  status: InterviewStatus;
  startTime?: Date;
  endTime?: Date;
  duration?: number; // in seconds
  recordingPath?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TranscriptSpeaker {
  id: string;
  name: string;
  color: string;
}

export interface TranscriptSegment {
  speaker: string; // speaker_id
  timestamp: string;
  text: string;
  confidence?: number;
}

export interface TranscriptData {
  speakers: TranscriptSpeaker[];
  segments: TranscriptSegment[];
  totalDuration?: number;
  language?: string;
}

export interface Statement {
  id: string;
  interviewId: string;
  content: string;
  officerNotes?: string;
  version: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface AudioRecording {
  id: string;
  interviewId: string;
  filePath: string;
  fileSize?: number;
  duration?: number;
  format?: string;
  sampleRate?: number;
  channels?: number;
  createdAt: Date;
}

export interface ExportLog {
  id: string;
  interviewId: string;
  exportType: ExportType;
  filePath?: string;
  exportedBy?: string;
  createdAt: Date;
}

// WebSocket message types
export interface TranscriptionMessage {
  type: 'transcription' | 'control' | 'error';
  data: TranscriptSegment | ControlMessage | ErrorMessage;
}

export interface ControlMessage {
  action: 'start' | 'pause' | 'resume' | 'stop';
  timestamp: string;
}

export interface ErrorMessage {
  code: string;
  message: string;
  details?: Record<string, any>;
}

// API response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  limit: number;
  offset: number;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: string;
}

// Utility types for forms and state management
export interface CaseFormData {
  caseId: string;
  incidentLocation: string;
  incidentDate: string;
  incidentTime: string;
}

export interface WitnessFormData {
  witnessName: string;
  witnessType: WitnessType;
  witnessContact: string;
  interviewEnvironment: InterviewEnvironment;
}

export interface StatementFormData {
  caseInfo: string;
  witnessInfo: string;
  statement: string;
  officerNotes: string;
}

// App state interface (matching existing app structure)
export interface AppState {
  currentCase: Case | null;
  currentWitness: Witness | null;
  isRecording: boolean;
  isPaused: boolean;
  recordingStartTime: number | null;
  transcriptData: TranscriptData | null;
  summaryData: string | null;
}

// Database transformation utilities type
export type DatabaseTransformer<TDatabase, TFrontend> = (db: TDatabase) => TFrontend;

// Supabase specific types
export interface SupabaseResponse<T> {
  data: T | null;
  error: {
    message: string;
    details: string;
    hint: string;
    code: string;
  } | null;
}

export interface SupabaseRealtimePayload<T> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any[];
}

// Query filter types for API endpoints
export interface CaseFilters {
  status?: CaseStatus;
  officer?: string;
  limit?: number;
  offset?: number;
}

export interface InterviewFilters {
  caseId?: string;
  status?: InterviewStatus;
  limit?: number;
  offset?: number;
}

// File upload types
export interface AudioUploadMetadata {
  interviewId: string;
  fileName: string;
  fileSize: number;
  duration?: number;
  format: string;
}

export interface ExportOptions {
  includeTranscript: boolean;
  includeAudio: boolean;
  includeOfficerNotes: boolean;
  format: ExportType;
}
