-- FIU Witness Interview Database Initialization Script
-- PostgreSQL/Supabase Database Schema

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types for better type safety
CREATE TYPE case_status AS ENUM ('In Progress', 'Completed');
CREATE TYPE interview_status AS ENUM ('scheduled', 'in_progress', 'completed', 'cancelled');
CREATE TYPE witness_type AS ENUM ('Resident', 'Neighbor', 'Passerby', 'Business Owner', 'Emergency Responder');
CREATE TYPE interview_environment AS ENUM ('controlled', 'field');
CREATE TYPE export_type AS ENUM ('pdf', 'docx');

-- Create cases table
CREATE TABLE cases (
    id VARCHAR(50) PRIMARY KEY,
    incident_location TEXT NOT NULL,
    incident_date DATE NOT NULL,
    incident_time TIME NOT NULL,
    officer VARCHAR(255) NOT NULL,
    status case_status NOT NULL DEFAULT 'In Progress',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create interviews table
CREATE TABLE interviews (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    case_id VARCHAR(50) NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
    witness_name VARCHAR(255) NOT NULL,
    witness_type witness_type NOT NULL,
    witness_contact VARCHAR(255),
    interview_environment interview_environment,
    status interview_status NOT NULL DEFAULT 'scheduled',
    start_time TIMESTAMPTZ,
    end_time TIMESTAMPTZ,
    duration_seconds INTEGER,
    recording_path TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_end_time_after_start CHECK (end_time IS NULL OR start_time IS NULL OR end_time > start_time),
    CONSTRAINT check_duration_positive CHECK (duration_seconds IS NULL OR duration_seconds > 0)
);

-- Create transcription_speakers table
CREATE TABLE transcription_speakers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE,
    speaker_id VARCHAR(10) NOT NULL,
    name VARCHAR(255) NOT NULL,
    color VARCHAR(7) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate speaker IDs per interview
    UNIQUE(interview_id, speaker_id)
);

-- Create transcription_segments table
CREATE TABLE transcription_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE,
    speaker_id VARCHAR(10) NOT NULL,
    timestamp VARCHAR(10) NOT NULL,
    text TEXT NOT NULL,
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    sequence_number INTEGER NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Unique constraint for sequence ordering
    UNIQUE(interview_id, sequence_number)
);

-- Create statements table
CREATE TABLE statements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE UNIQUE,
    content TEXT NOT NULL,
    officer_notes TEXT,
    version INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_version_positive CHECK (version > 0)
);

-- Create audio_recordings table
CREATE TABLE audio_recordings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE UNIQUE,
    file_path TEXT NOT NULL,
    file_size BIGINT,
    duration_seconds INTEGER,
    format VARCHAR(10),
    sample_rate INTEGER,
    channels INTEGER,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT check_file_size_positive CHECK (file_size IS NULL OR file_size > 0),
    CONSTRAINT check_duration_positive CHECK (duration_seconds IS NULL OR duration_seconds > 0),
    CONSTRAINT check_sample_rate_positive CHECK (sample_rate IS NULL OR sample_rate > 0),
    CONSTRAINT check_channels_positive CHECK (channels IS NULL OR channels > 0)
);

-- Create export_logs table
CREATE TABLE export_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    interview_id UUID NOT NULL REFERENCES interviews(id) ON DELETE CASCADE,
    export_type export_type NOT NULL,
    file_path TEXT,
    exported_by VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_cases_status ON cases(status);
CREATE INDEX idx_cases_officer ON cases(officer);
CREATE INDEX idx_cases_incident_date ON cases(incident_date);
CREATE INDEX idx_interviews_case_id ON interviews(case_id);
CREATE INDEX idx_interviews_status ON interviews(status);
CREATE INDEX idx_interviews_start_time ON interviews(start_time);
CREATE INDEX idx_transcription_segments_interview_id ON transcription_segments(interview_id);
CREATE INDEX idx_transcription_segments_sequence ON transcription_segments(interview_id, sequence_number);
CREATE INDEX idx_transcription_speakers_interview_id ON transcription_speakers(interview_id);
CREATE INDEX idx_statements_interview_id ON statements(interview_id);
CREATE INDEX idx_audio_recordings_interview_id ON audio_recordings(interview_id);
CREATE INDEX idx_export_logs_interview_id ON export_logs(interview_id);
CREATE INDEX idx_export_logs_created_at ON export_logs(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic updated_at timestamp updates
CREATE TRIGGER update_cases_updated_at 
    BEFORE UPDATE ON cases 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_interviews_updated_at 
    BEFORE UPDATE ON interviews 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_statements_updated_at 
    BEFORE UPDATE ON statements 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to increment statement version on content update
CREATE OR REPLACE FUNCTION increment_statement_version()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.content IS DISTINCT FROM NEW.content THEN
        NEW.version = OLD.version + 1;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for statement version increment
CREATE TRIGGER increment_statement_version_trigger
    BEFORE UPDATE ON statements
    FOR EACH ROW
    EXECUTE FUNCTION increment_statement_version();

-- Create function to validate speaker_id exists in transcription_speakers
CREATE OR REPLACE FUNCTION validate_speaker_id()
RETURNS TRIGGER AS $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM transcription_speakers 
        WHERE interview_id = NEW.interview_id 
        AND speaker_id = NEW.speaker_id
    ) THEN
        RAISE EXCEPTION 'Speaker ID % does not exist for interview %', NEW.speaker_id, NEW.interview_id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to validate speaker_id in transcription_segments
CREATE TRIGGER validate_speaker_id_trigger
    BEFORE INSERT OR UPDATE ON transcription_segments
    FOR EACH ROW
    EXECUTE FUNCTION validate_speaker_id();

-- Enable Row Level Security (RLS)
ALTER TABLE cases ENABLE ROW LEVEL SECURITY;
ALTER TABLE interviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE transcription_speakers ENABLE ROW LEVEL SECURITY;
ALTER TABLE transcription_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE statements ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_recordings ENABLE ROW LEVEL SECURITY;
ALTER TABLE export_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (basic policies - adjust based on your auth requirements)
-- Note: These policies assume you have user authentication set up in Supabase

-- Cases: Officers can access their assigned cases
CREATE POLICY "Officers can view their assigned cases" ON cases
    FOR SELECT USING (auth.jwt() ->> 'email' = officer OR auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Officers can update their assigned cases" ON cases
    FOR UPDATE USING (auth.jwt() ->> 'email' = officer OR auth.jwt() ->> 'role' = 'admin');

CREATE POLICY "Officers can insert new cases" ON cases
    FOR INSERT WITH CHECK (auth.jwt() ->> 'email' = officer OR auth.jwt() ->> 'role' = 'admin');

-- Interviews: Access based on case access
CREATE POLICY "Users can view interviews for accessible cases" ON interviews
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM cases 
            WHERE cases.id = interviews.case_id 
            AND (cases.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

CREATE POLICY "Users can modify interviews for accessible cases" ON interviews
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM cases 
            WHERE cases.id = interviews.case_id 
            AND (cases.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

-- Transcription data: Access based on interview access
CREATE POLICY "Users can access transcription speakers for accessible interviews" ON transcription_speakers
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = transcription_speakers.interview_id 
            AND (c.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

CREATE POLICY "Users can access transcription segments for accessible interviews" ON transcription_segments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = transcription_segments.interview_id 
            AND (c.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

-- Statements: Access based on interview access
CREATE POLICY "Users can access statements for accessible interviews" ON statements
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = statements.interview_id 
            AND (c.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

-- Audio recordings: Access based on interview access
CREATE POLICY "Users can access audio recordings for accessible interviews" ON audio_recordings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = audio_recordings.interview_id 
            AND (c.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

-- Export logs: Access based on interview access
CREATE POLICY "Users can access export logs for accessible interviews" ON export_logs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM interviews i
            JOIN cases c ON c.id = i.case_id
            WHERE i.id = export_logs.interview_id 
            AND (c.officer = auth.jwt() ->> 'email' OR auth.jwt() ->> 'role' = 'admin')
        )
    );

-- Insert sample data for testing
INSERT INTO cases (id, incident_location, incident_date, incident_time, officer, status) VALUES
('FIU-2025-001', '123 Main Street, Apartment 4B', '2025-06-20', '14:30', '<EMAIL>', 'In Progress'),
('FIU-2025-002', '456 Oak Avenue, Single Family Home', '2025-06-18', '09:15', '<EMAIL>', 'Completed');

-- Create storage buckets (run these in Supabase dashboard or via API)
-- INSERT INTO storage.buckets (id, name, public) VALUES ('audio-recordings', 'audio-recordings', false);
-- INSERT INTO storage.buckets (id, name, public) VALUES ('exported-documents', 'exported-documents', false);

COMMIT;
